import React, {useEffect, useState} from 'react';
import {Modal, Button} from 'react-bootstrap';
import {getTemplateById} from '../services/services';
import {createFormFromData} from '../utils/helper';
import {TemplateForm} from '../types';
import PreviewFormDetails from '../pages/CreateRA/PreviewFormDetails';
import {useNavigate} from 'react-router-dom';

type Props = {
  onClose: () => void;
  id: number;
  canUseTemplate?: boolean;
};

export const PreviewTemplateModal: React.FC<Props> = ({
  onClose,
  id,
  canUseTemplate = true,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [form, setForm] = useState<TemplateForm>(createFormFromData());
  const navigate = useNavigate();

  const fetchTemplateData = async (templateId: string) => {
    setIsLoading(true);
    try {
      const response = await getTemplateById(templateId);
      const data = response.result;
      const formData = createFormFromData(data);
      setForm(formData);
    } catch (err) {
      console.error('Error fetching draft', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!id) return;
    fetchTemplateData(id.toString());
  }, [id]);

  return (
    <Modal
      show
      onHide={onClose}
      backdrop="static"
      dialogClassName="top-modal preview-template-modal"
    >
      <Modal.Header>
        <Modal.Title>Preview: {form.task_requiring_ra}</Modal.Title>
      </Modal.Header>
      <Modal.Body className="edit-modal-body">
        {isLoading ? (
          <div className="d-flex justify-content-center align-items-center">
            <div className="spinner-border text-primary"></div>
          </div>
        ) : (
          <PreviewFormDetails
            form={form}
            setForm={setForm}
            atRiskRef={{current: null}}
            handlePreviewPublush={() => {}}
            handleSaveToDraft={() => {}}
            type="template"
            previewOnly={true}
          />
        )}
      </Modal.Body>

      <Modal.Footer>
        <Button
          variant="primary"
          className="me-2 fs-14"
          onClick={() => onClose()}
        >
          Cancel
        </Button>
        {canUseTemplate && (
          <Button
            variant="secondary"
            className="me-2 fs-14"
            disabled={isLoading}
            onClick={() =>
              navigate(`/risk-assessment/templates/${id}/risks/create`)
            }
          >
            Use Template
          </Button>
        )}
      </Modal.Footer>
    </Modal>
  );
};
