import React, {forwardRef, useImperativeHandle} from 'react';
import SelectableCheckboxGrid from '../../components/SelectableCheckboxGrid';
import {TemplateForm} from '../../types/template';
import {RiskForm} from '../../types/risk';
import {useDataStoreContext} from '../../context';

export const HazardCategoryStep = forwardRef(
  (
    {
      form,
      setForm,
      onValidate,
      isEdit = false,
      type = 'template',
    }: {
      form: TemplateForm | RiskForm;
      setForm: any;
      onValidate?: (valid: boolean) => void;
      isEdit?: boolean;
      type?: 'template' | 'risk';
    },
    ref,
  ) => {
    const {
      dataStore: {hazardsList},
    } = useDataStoreContext();

    const hazardCategories = hazardsList || [];

    // Validation logic
    const validate = () => {
      let selected: number[] = [];
      let isOthers = false;
      let othersText = '';

      if (type === 'risk') {
        const riskForm = form as RiskForm;
        selected = riskForm?.risk_hazard?.hazard_id || [];
        isOthers = riskForm?.risk_hazard?.is_other ?? false;
        othersText = riskForm?.risk_hazard?.value || '';
      } else {
        const templateForm = form as TemplateForm;
        selected = templateForm?.template_hazard?.hazard_id || [];
        isOthers = templateForm?.template_hazard?.is_other ?? false;
        othersText = templateForm?.template_hazard?.value || '';
      }

      let valid = selected.length > 0 || isOthers;
      if (isOthers) {
        valid = !!othersText.trim();
      }
      if (onValidate) onValidate(valid);
      return valid;
    };

    useImperativeHandle(ref, () => ({
      validate,
    }));

    // Extract dependencies for useEffect
    const hazardId =
      type === 'risk'
        ? (form as RiskForm).risk_hazard?.hazard_id
        : (form as TemplateForm).template_hazard?.hazard_id;
    const isOther =
      type === 'risk'
        ? (form as RiskForm).risk_hazard?.is_other
        : (form as TemplateForm).template_hazard?.is_other;
    const hazardValue =
      type === 'risk'
        ? (form as RiskForm).risk_hazard?.value
        : (form as TemplateForm).template_hazard?.value;

    // Validate on every relevant form change
    React.useEffect(() => {
      validate();
      // eslint-disable-next-line
    }, [hazardId, isOther, hazardValue]);

    const handleOthersChange = (flag: boolean, value: string) => {
      if (type === 'risk') {
        setForm((prev: RiskForm) => ({
          ...prev,
          risk_hazard: {
            ...prev.risk_hazard,
            is_other: flag,
            value: flag ? value : '',
          },
        }));
      } else {
        setForm((prev: TemplateForm) => ({
          ...prev,
          template_hazard: {
            ...prev.template_hazard,
            is_other: flag,
            value: flag ? value : '',
          },
        }));
      }
    };

    const handleCheckedChange = (ids: number[]) => {
      if (type === 'risk') {
        setForm((prev: RiskForm) => ({
          ...prev,
          risk_hazard: {
            ...prev.risk_hazard,
            hazard_id: ids,
          },
        }));
      } else {
        setForm((prev: TemplateForm) => ({
          ...prev,
          template_hazard: {
            ...prev.template_hazard,
            hazard_id: ids,
          },
        }));
      }
    };

    // Get the appropriate values based on form type
    const getHazardData = () => {
      if (type === 'risk') {
        const riskForm = form as RiskForm;
        return {
          initialChecked: riskForm?.risk_hazard?.hazard_id || [],
          isOthersSelected: riskForm?.risk_hazard?.is_other || false,
          othersText: riskForm?.risk_hazard?.value || '',
        };
      } else {
        const templateForm = form as TemplateForm;
        return {
          initialChecked: templateForm?.template_hazard?.hazard_id || [],
          isOthersSelected: templateForm?.template_hazard?.is_other || false,
          othersText: templateForm?.template_hazard?.value || '',
        };
      }
    };

    const hazardData = getHazardData();

    return (
      <SelectableCheckboxGrid
        title={form?.task_requiring_ra || ''}
        subtitle="Select all the possible Hazard Category"
        searchPlaceholder="Search Hazard Category"
        options={hazardCategories}
        initialChecked={hazardData.initialChecked}
        isOthersSelected={hazardData.isOthersSelected}
        othersText={hazardData.othersText}
        onOthersChange={handleOthersChange}
        onChange={handleCheckedChange}
        hasOthers={true}
        isEdit={isEdit}
        dateOfRiskAssessment={
          type === 'risk' ? (form as RiskForm)?.date_risk_assessment ?? '' : ''
        }
      />
    );
  },
);

HazardCategoryStep.displayName = 'HazardCategoryStep';
