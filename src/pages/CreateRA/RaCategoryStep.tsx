import React, {forwardRef, useImperativeHandle} from 'react';
import SelectableCheckboxGrid from '../../components/SelectableCheckboxGrid';
import {TemplateForm} from '../../types/template';
import {RiskForm} from '../../types/risk';
import {useDataStoreContext} from '../../context';

export const RaCategoryStep = forwardRef(
  (
    {
      form,
      setForm,
      onValidate,
      isEdit = false,
      type = 'template',
    }: {
      form: TemplateForm | RiskForm;
      setForm: any;
      onValidate?: (valid: boolean) => void;
      isEdit?: boolean;
      type?: 'template' | 'risk';
    },
    ref,
  ) => {
    const {
      dataStore: {riskCategoryList},
    } = useDataStoreContext();

    // Validation logic
    const validate = () => {
      let selected: number[] = [];

      if (type === 'risk') {
        selected = (form as RiskForm)?.risk_category?.category_id || [];
      } else {
        selected = (form as TemplateForm)?.template_category?.category_id || [];
      }

      const valid = selected.length > 0;

      if (onValidate) onValidate(valid);
      return valid;
    };

    useImperativeHandle(ref, () => ({
      validate,
    }));

    // Validate on every relevant form change
    const categoryIds =
      type === 'risk'
        ? (form as RiskForm).risk_category?.category_id
        : (form as TemplateForm).template_category?.category_id;

    React.useEffect(() => {
      validate();
      // eslint-disable-next-line
    }, [categoryIds]);

    const handleOthersChange = (flag: boolean, value: string) => {
      if (type === 'risk') {
        setForm((prev: RiskForm) => ({
          ...prev,
          risk_category: {
            ...prev.risk_category,
            is_other: flag,
            value: flag ? value : '',
          },
        }));
      } else {
        setForm((prev: TemplateForm) => ({
          ...prev,
          template_category: {
            ...prev.template_category,
            is_other: flag,
            value: flag ? value : '',
          },
        }));
      }
    };

    const handleCheckedChange = (ids: number[]) => {
      if (type === 'risk') {
        setForm((prev: RiskForm) => ({
          ...prev,
          risk_category: {
            ...prev.risk_category,
            category_id: ids,
          },
        }));
      } else {
        setForm((prev: TemplateForm) => ({
          ...prev,
          template_category: {
            ...prev.template_category,
            category_id: ids,
          },
        }));
      }
    };

    // Get the appropriate values based on form type
    const getCategoryData = () => {
      if (type === 'risk') {
        const riskForm = form as RiskForm;
        return {
          initialChecked: riskForm?.risk_category?.category_id || [],
          isOthersSelected: riskForm?.risk_category?.is_other || false,
          othersText: riskForm?.risk_category?.value || '',
        };
      } else {
        const templateForm = form as TemplateForm;
        return {
          initialChecked: templateForm?.template_category?.category_id || [],
          isOthersSelected: false,
          othersText: '',
        };
      }
    };

    const categoryData = getCategoryData();

    return (
      <SelectableCheckboxGrid
        title={form?.task_requiring_ra || ''}
        subtitle="Select all the R.A. Category"
        searchPlaceholder="Search RA Category"
        options={riskCategoryList}
        initialChecked={categoryData.initialChecked}
        isOthersSelected={categoryData.isOthersSelected}
        othersText={categoryData.othersText}
        onOthersChange={handleOthersChange}
        onChange={handleCheckedChange}
        isEdit={isEdit}
        dateOfRiskAssessment={
          type === 'risk' ? (form as RiskForm)?.date_risk_assessment ?? '' : ''
        }
      />
    );
  },
);

RaCategoryStep.displayName = 'RaCategoryStep';
